// File upload utilities for chat file sharing

export interface FileUploadResult {
  success: boolean;
  fileUrl?: string;
  thumbnailUrl?: string;
  error?: string;
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

// File type configurations
export const FILE_TYPES = {
  images: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  documents: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
  archives: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'],
} as const;

export const ALLOWED_FILE_TYPES = [
  ...FILE_TYPES.images,
  ...FILE_TYPES.documents,
  ...FILE_TYPES.archives,
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB for images

export function validateFile(file: File): FileValidationResult {
  // Check file size
  const maxSize = FILE_TYPES.images.includes(file.type as any) ? MAX_IMAGE_SIZE : MAX_FILE_SIZE;
  if (file.size > maxSize) {
    const maxSizeMB = maxSize / (1024 * 1024);
    return {
      isValid: false,
      error: `File size must be less than ${maxSizeMB}MB`
    };
  }

  // Check file type
  if (!ALLOWED_FILE_TYPES.includes(file.type as any)) {
    return {
      isValid: false,
      error: 'File type not supported. Allowed types: images, PDFs, documents, and archives'
    };
  }

  // Check file name length
  if (file.name.length > 255) {
    return {
      isValid: false,
      error: 'File name is too long (max 255 characters)'
    };
  }

  return { isValid: true };
}

export function getFileTypeCategory(mimeType: string): 'image' | 'document' | 'archive' | 'other' {
  if (FILE_TYPES.images.includes(mimeType as any)) return 'image';
  if (FILE_TYPES.documents.includes(mimeType as any)) return 'document';
  if (FILE_TYPES.archives.includes(mimeType as any)) return 'archive';
  return 'other';
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function getFileIcon(mimeType: string): string {
  if (FILE_TYPES.images.includes(mimeType as any)) return '🖼️';
  if (mimeType === 'application/pdf') return '📄';
  if (FILE_TYPES.documents.includes(mimeType as any)) return '📝';
  if (FILE_TYPES.archives.includes(mimeType as any)) return '📦';
  return '📎';
}

// Create thumbnail for images
export function createImageThumbnail(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!FILE_TYPES.images.includes(file.type as any)) {
      reject(new Error('File is not an image'));
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate thumbnail dimensions (max 200x200, maintain aspect ratio)
      const maxSize = 200;
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxSize) {
          height = (height * maxSize) / width;
          width = maxSize;
        }
      } else {
        if (height > maxSize) {
          width = (width * maxSize) / height;
          height = maxSize;
        }
      }

      canvas.width = width;
      canvas.height = height;
      
      if (ctx) {
        ctx.drawImage(img, 0, 0, width, height);
        resolve(canvas.toDataURL('image/jpeg', 0.8));
      } else {
        reject(new Error('Failed to create canvas context'));
      }
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

// Mock upload function - in production, this would upload to your storage service
export async function uploadFile(file: File): Promise<FileUploadResult> {
  try {
    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error
      };
    }

    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Create object URL (in production, this would be the actual uploaded file URL)
    const fileUrl = URL.createObjectURL(file);
    
    let thumbnailUrl: string | undefined;
    
    // Create thumbnail for images
    if (FILE_TYPES.images.includes(file.type as any)) {
      try {
        thumbnailUrl = await createImageThumbnail(file);
      } catch (error) {
        console.warn('Failed to create thumbnail:', error);
      }
    }

    return {
      success: true,
      fileUrl,
      thumbnailUrl
    };
    
  } catch (error) {
    return {
      success: false,
      error: 'Upload failed. Please try again.'
    };
  }
}

// Cleanup object URLs to prevent memory leaks
export function cleanupFileUrl(url: string) {
  if (url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
}

// File preview component data
export interface FilePreview {
  file: File;
  url: string;
  thumbnailUrl?: string;
  category: 'image' | 'document' | 'archive' | 'other';
  icon: string;
}

export function createFilePreview(file: File): FilePreview {
  const url = URL.createObjectURL(file);
  const category = getFileTypeCategory(file.type);
  const icon = getFileIcon(file.type);
  
  return {
    file,
    url,
    category,
    icon
  };
}