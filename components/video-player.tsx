"use client";

import React from 'react';
import {
  LiveKitRoom,
  VideoConference,
  GridLayout,
  ParticipantTile,
  RoomAudioRenderer,
  useTracks,
} from '@livekit/components-react';
import { Track } from 'livekit-client';
import '@livekit/components-styles';

interface VideoPlayerProps {
  token: string;
  room: string;
  children?: React.ReactNode;
}

function CustomVideoConference() {
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );

  return (
    <div className="h-full flex flex-col">
      <GridLayout tracks={tracks} style={{ height: '100%' }}>
        <ParticipantTile />
      </GridLayout>
    </div>
  );
}

export function VideoPlayer({ token, room, children }: VideoPlayerProps) {
  const serverUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL || 'wss://streamyard-clonez-1zofz2li.livekit.cloud';

  const handleLiveKitError = (error: Error) => {
    console.error('LiveKit error:', error);

    // Filter out common harmless errors
    if (error.message?.includes('The object can not be found here')) {
      console.warn('Caught DOM manipulation error in LiveKit (likely harmless):', error.message);
      return;
    }

    // Log other errors for debugging
    console.error('LiveKit video player error:', {
      message: error.message,
      stack: error.stack,
      room
    });
  };

  return (
    <div className="w-full h-full bg-black rounded-lg overflow-hidden">
      <LiveKitRoom
        video={true}
        audio={true}
        token={token}
        serverUrl={serverUrl}
        data-lk-theme="default"
        style={{ height: '100%' }}
        onConnected={() => console.log('Connected to room:', room)}
        onDisconnected={() => console.log('Disconnected from room:', room)}
        onError={handleLiveKitError}
      >
        <CustomVideoConference />
        <RoomAudioRenderer />
        {children}
      </LiveKitRoom>
    </div>
  );
}