"use client";

import React, { useState, useRef, useEffect } from 'react';
import {
  Send,
  Paperclip,
  Smile,
  MoreHorizontal,
  Reply,
  Edit3,
  Trash2,
  Flag,
  Copy,
  Download,
  Image,
  FileText,
  AlertTriangle,
  Shield,
  Clock,
  MessageSquare,
  X,
  ChevronDown,
  Search,
  Filter,
  Settings
} from 'lucide-react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';

interface AdvancedChatProps {
  roomId: Id<"rooms">;
  currentUserId: string;
  currentUserRole: string;
  className?: string;
}

interface ChatMessage {
  _id: Id<"chatMessages">;
  userId: string;
  userName: string;
  message: string;
  messageType: 'text' | 'file' | 'image' | 'system' | 'announcement';
  fileData?: {
    fileName: string;
    fileSize: number;
    fileType: string;
    fileUrl: string;
    thumbnailUrl?: string;
  };
  replyToMessageId?: Id<"chatMessages">;
  threadId?: string;
  isEdited: boolean;
  editedAt?: number;
  isDeleted: boolean;
  isFlagged: boolean;
  flaggedReason?: string;
  timestamp: number;
  reactions: Record<string, Array<{ userId: string; userName: string }>>;
  reactionCount: number;
}

const EMOJI_REACTIONS = ['👍', '❤️', '😂', '😮', '😢', '😡', '👏', '🔥'];

export function AdvancedChat({ roomId, currentUserId, currentUserRole, className = "" }: AdvancedChatProps) {
  const [message, setMessage] = useState('');
  const [replyingTo, setReplyingTo] = useState<ChatMessage | null>(null);
  const [editingMessage, setEditingMessage] = useState<ChatMessage | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showModeration, setShowModeration] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'files' | 'images' | 'flagged'>('all');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);

  // Queries
  const messages = useQuery(api.chat.getMessages, { roomId, limit: 100 }) as ChatMessage[] | undefined;
  const userChatStatus = useQuery(api.chat.getUserChatStatus, { roomId });
  const moderationActions = useQuery(api.chat.getModerationActions, { roomId, limit: 20 });

  // Mutations
  const sendMessageMutation = useMutation(api.chat.sendMessage);
  const addReactionMutation = useMutation(api.chat.addReaction);
  const editMessageMutation = useMutation(api.chat.editMessage);
  const deleteMessageMutation = useMutation(api.chat.deleteMessage);
  const flagMessageMutation = useMutation(api.chat.flagMessage);
  const moderateUserMutation = useMutation(api.chat.moderateUser);

  const canModerate = ['host', 'co-host', 'moderator'].includes(currentUserRole);
  const canSendMessages = userChatStatus?.canSendMessages && !userChatStatus?.isMuted && !userChatStatus?.isBanned;
  const canSendFiles = userChatStatus?.canSendFiles && canSendMessages;

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when replying or editing
  useEffect(() => {
    if (replyingTo || editingMessage) {
      messageInputRef.current?.focus();
    }
  }, [replyingTo, editingMessage]);

  const handleSendMessage = async () => {
    if (!message.trim() && !selectedFile) return;
    if (!canSendMessages) return;

    try {
      let fileData = undefined;
      
      if (selectedFile) {
        // In a real app, you'd upload the file to storage first
        fileData = {
          fileName: selectedFile.name,
          fileSize: selectedFile.size,
          fileType: selectedFile.type,
          fileUrl: URL.createObjectURL(selectedFile), // Temporary URL
        };
      }

      await sendMessageMutation({
        roomId,
        message: message.trim() || (selectedFile ? `Shared ${selectedFile.name}` : ''),
        messageType: selectedFile?.type.startsWith('image/') ? 'image' : selectedFile ? 'file' : 'text',
        fileData,
        replyToMessageId: replyingTo?._id,
        threadId: replyingTo?.threadId || replyingTo?._id,
      });

      setMessage('');
      setSelectedFile(null);
      setReplyingTo(null);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleEditMessage = async () => {
    if (!editingMessage || !message.trim()) return;

    try {
      await editMessageMutation({
        messageId: editingMessage._id,
        newMessage: message.trim(),
      });
      setMessage('');
      setEditingMessage(null);
    } catch (error) {
      console.error('Failed to edit message:', error);
    }
  };

  const handleDeleteMessage = async (messageId: Id<"chatMessages">) => {
    try {
      await deleteMessageMutation({ messageId });
    } catch (error) {
      console.error('Failed to delete message:', error);
    }
  };

  const handleAddReaction = async (messageId: Id<"chatMessages">, emoji: string) => {
    try {
      await addReactionMutation({ messageId, reaction: emoji });
      setShowEmojiPicker(null);
    } catch (error) {
      console.error('Failed to add reaction:', error);
    }
  };

  const handleFlagMessage = async (messageId: Id<"chatMessages">, reason: string) => {
    try {
      await flagMessageMutation({ messageId, reason });
    } catch (error) {
      console.error('Failed to flag message:', error);
    }
  };

  const handleModerateUser = async (userId: string, action: string, duration?: number, reason?: string) => {
    try {
      await moderateUserMutation({
        roomId,
        userId,
        action: action as any,
        duration,
        reason,
      });
    } catch (error) {
      console.error('Failed to moderate user:', error);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB');
        return;
      }
      setSelectedFile(file);
    }
  };

  const startEdit = (msg: ChatMessage) => {
    setEditingMessage(msg);
    setMessage(msg.message);
    setReplyingTo(null);
  };

  const startReply = (msg: ChatMessage) => {
    setReplyingTo(msg);
    setEditingMessage(null);
    setMessage('');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const filteredMessages = messages?.filter(msg => {
    if (searchTerm && !msg.message.toLowerCase().includes(searchTerm.toLowerCase()) && !msg.userName.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    switch (filterType) {
      case 'files':
        return msg.messageType === 'file';
      case 'images':
        return msg.messageType === 'image';
      case 'flagged':
        return msg.isFlagged;
      default:
        return true;
    }
  }) || [];

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-gray-600" />
          <h3 className="font-semibold text-gray-900">Chat</h3>
          <span className="text-sm text-gray-500">({messages?.length || 0} messages)</span>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Search and Filter */}
          <div className="flex items-center space-x-1">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-2 top-2.5 text-gray-400" />
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 w-32"
              />
            </div>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-2 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All</option>
              <option value="files">Files</option>
              <option value="images">Images</option>
              <option value="flagged">Flagged</option>
            </select>
          </div>

          {canModerate && (
            <button
              onClick={() => setShowModeration(!showModeration)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              title="Moderation tools"
            >
              <Shield className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Moderation Panel */}
      {showModeration && canModerate && (
        <div className="p-4 bg-yellow-50 border-b border-yellow-200">
          <h4 className="font-medium text-yellow-800 mb-2">Moderation Actions</h4>
          <div className="text-sm text-yellow-700">
            Recent actions: {moderationActions?.length || 0}
          </div>
        </div>
      )}

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {filteredMessages.map((msg) => (
          <MessageComponent
            key={msg._id}
            message={msg}
            currentUserId={currentUserId}
            canModerate={canModerate}
            onReply={startReply}
            onEdit={startEdit}
            onDelete={handleDeleteMessage}
            onAddReaction={handleAddReaction}
            onFlag={handleFlagMessage}
            onModerateUser={handleModerateUser}
            showEmojiPicker={showEmojiPicker === msg._id}
            setShowEmojiPicker={setShowEmojiPicker}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Reply/Edit Context */}
      {(replyingTo || editingMessage) && (
        <div className="px-4 py-2 bg-blue-50 border-t border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-sm">
              {replyingTo && (
                <>
                  <Reply className="w-4 h-4 text-blue-600" />
                  <span className="text-blue-600">Replying to {replyingTo.userName}</span>
                  <span className="text-gray-500 truncate max-w-xs">{replyingTo.message}</span>
                </>
              )}
              {editingMessage && (
                <>
                  <Edit3 className="w-4 h-4 text-blue-600" />
                  <span className="text-blue-600">Editing message</span>
                </>
              )}
            </div>
            <button
              onClick={() => {
                setReplyingTo(null);
                setEditingMessage(null);
                setMessage('');
              }}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* File Preview */}
      {selectedFile && (
        <div className="px-4 py-2 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-sm">
              {selectedFile.type.startsWith('image/') ? (
                <Image className="w-4 h-4 text-blue-600" />
              ) : (
                <FileText className="w-4 h-4 text-blue-600" />
              )}
              <span className="text-gray-900">{selectedFile.name}</span>
              <span className="text-gray-500">({formatFileSize(selectedFile.size)})</span>
            </div>
            <button
              onClick={() => setSelectedFile(null)}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200">
        {userChatStatus?.isMuted && (
          <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2 text-sm text-red-700">
              <AlertTriangle className="w-4 h-4" />
              <span>You are muted and cannot send messages</span>
            </div>
          </div>
        )}
        
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <textarea
              ref={messageInputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={canSendMessages ? "Type a message..." : "You cannot send messages"}
              disabled={!canSendMessages}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
              rows={1}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  if (editingMessage) {
                    handleEditMessage();
                  } else {
                    handleSendMessage();
                  }
                }
              }}
            />
          </div>
          
          {/* File Upload */}
          {canSendFiles && (
            <>
              <input
                ref={fileInputRef}
                type="file"
                onChange={handleFileSelect}
                className="hidden"
                accept="image/*,.pdf,.doc,.docx,.txt,.zip"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                title="Attach file"
              >
                <Paperclip className="w-5 h-5" />
              </button>
            </>
          )}
          
          {/* Send Button */}
          <button
            onClick={editingMessage ? handleEditMessage : handleSendMessage}
            disabled={!canSendMessages || (!message.trim() && !selectedFile)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {editingMessage ? <Edit3 className="w-5 h-5" /> : <Send className="w-5 h-5" />}
          </button>
        </div>
      </div>
    </div>
  );
}

// Individual Message Component
interface MessageComponentProps {
  message: ChatMessage;
  currentUserId: string;
  canModerate: boolean;
  onReply: (msg: ChatMessage) => void;
  onEdit: (msg: ChatMessage) => void;
  onDelete: (id: Id<"chatMessages">) => void;
  onAddReaction: (id: Id<"chatMessages">, emoji: string) => void;
  onFlag: (id: Id<"chatMessages">, reason: string) => void;
  onModerateUser: (userId: string, action: string, duration?: number, reason?: string) => void;
  showEmojiPicker: boolean;
  setShowEmojiPicker: (id: string | null) => void;
}

function MessageComponent({
  message,
  currentUserId,
  canModerate,
  onReply,
  onEdit,
  onDelete,
  onAddReaction,
  onFlag,
  onModerateUser,
  showEmojiPicker,
  setShowEmojiPicker,
}: MessageComponentProps) {
  const [showActions, setShowActions] = useState(false);
  const [showModerationMenu, setShowModerationMenu] = useState(false);
  
  const isOwnMessage = message.userId === currentUserId;
  const canEditDelete = isOwnMessage && Date.now() - message.timestamp < 15 * 60 * 1000; // 15 minutes

  const renderFileMessage = () => {
    if (!message.fileData) return null;

    const { fileName, fileSize, fileType, fileUrl, thumbnailUrl } = message.fileData;

    if (message.messageType === 'image') {
      return (
        <div className="mt-2">
          <img
            src={thumbnailUrl || fileUrl}
            alt={fileName}
            className="max-w-xs rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
            onClick={() => window.open(fileUrl, '_blank')}
          />
          <div className="mt-1 text-xs text-gray-500">
            {fileName} • {formatFileSize(fileSize)}
          </div>
        </div>
      );
    }

    return (
      <div className="mt-2 p-3 bg-gray-50 rounded-lg border border-gray-200 max-w-xs">
        <div className="flex items-center space-x-2">
          <FileText className="w-6 h-6 text-blue-600 flex-shrink-0" />
          <div className="min-w-0 flex-1">
            <div className="font-medium text-sm text-gray-900 truncate">{fileName}</div>
            <div className="text-xs text-gray-500">{formatFileSize(fileSize)}</div>
          </div>
          <button
            onClick={() => window.open(fileUrl, '_blank')}
            className="p-1 text-gray-600 hover:text-gray-900"
            title="Download"
          >
            <Download className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div
      className={`group relative ${isOwnMessage ? 'ml-12' : 'mr-12'}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => {
        setShowActions(false);
        setShowModerationMenu(false);
        setShowEmojiPicker(null);
      }}
    >
      <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
        <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'bg-blue-600 text-white' : 'bg-gray-100'} rounded-lg px-4 py-2`}>
          {/* Message Header */}
          <div className="flex items-center justify-between mb-1">
            <span className={`font-medium text-sm ${isOwnMessage ? 'text-blue-100' : 'text-gray-600'}`}>
              {message.userName}
            </span>
            <div className="flex items-center space-x-1">
              {message.isEdited && (
                <span className={`text-xs ${isOwnMessage ? 'text-blue-200' : 'text-gray-400'}`}>
                  (edited)
                </span>
              )}
              {message.isFlagged && (
                <Flag className={`w-3 h-3 ${isOwnMessage ? 'text-red-200' : 'text-red-500'}`} />
              )}
              <span className={`text-xs ${isOwnMessage ? 'text-blue-200' : 'text-gray-400'}`}>
                {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </span>
            </div>
          </div>

          {/* Message Content */}
          <div className="text-sm">
            {message.message}
            {renderFileMessage()}
          </div>

          {/* Reactions */}
          {Object.keys(message.reactions).length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {Object.entries(message.reactions).map(([emoji, users]) => (
                <button
                  key={emoji}
                  onClick={() => onAddReaction(message._id, emoji)}
                  className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs transition-colors ${
                    users.some(u => u.userId === currentUserId)
                      ? 'bg-blue-500 text-white'
                      : isOwnMessage
                      ? 'bg-blue-500 bg-opacity-20 text-blue-100 hover:bg-opacity-30'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                  title={users.map(u => u.userName).join(', ')}
                >
                  <span>{emoji}</span>
                  <span>{users.length}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      {showActions && (
        <div className={`absolute top-0 ${isOwnMessage ? 'left-0' : 'right-0'} flex items-center space-x-1 -mt-8 bg-white border border-gray-200 rounded-lg px-2 py-1 shadow-lg z-10`}>
          {/* React Button */}
          <div className="relative">
            <button
              onClick={() => setShowEmojiPicker(showEmojiPicker ? null : message._id)}
              className="p-1 text-gray-600 hover:text-gray-900 rounded"
              title="Add reaction"
            >
              <Smile className="w-4 h-4" />
            </button>
            
            {showEmojiPicker && (
              <div className="absolute bottom-full mb-2 left-0 bg-white border border-gray-200 rounded-lg p-2 shadow-lg z-20">
                <div className="grid grid-cols-4 gap-1">
                  {EMOJI_REACTIONS.map((emoji) => (
                    <button
                      key={emoji}
                      onClick={() => onAddReaction(message._id, emoji)}
                      className="p-2 hover:bg-gray-100 rounded text-lg"
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Reply Button */}
          <button
            onClick={() => onReply(message)}
            className="p-1 text-gray-600 hover:text-gray-900 rounded"
            title="Reply"
          >
            <Reply className="w-4 h-4" />
          </button>

          {/* Edit Button (own messages only) */}
          {canEditDelete && (
            <button
              onClick={() => onEdit(message)}
              className="p-1 text-gray-600 hover:text-gray-900 rounded"
              title="Edit"
            >
              <Edit3 className="w-4 h-4" />
            </button>
          )}

          {/* More Actions */}
          <div className="relative">
            <button
              onClick={() => setShowModerationMenu(!showModerationMenu)}
              className="p-1 text-gray-600 hover:text-gray-900 rounded"
              title="More actions"
            >
              <MoreHorizontal className="w-4 h-4" />
            </button>
            
            {showModerationMenu && (
              <div className="absolute bottom-full mb-2 right-0 bg-white border border-gray-200 rounded-lg py-1 shadow-lg z-20 min-w-32">
                <button
                  onClick={() => navigator.clipboard.writeText(message.message)}
                  className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                >
                  <Copy className="w-4 h-4" />
                  <span>Copy</span>
                </button>
                
                {(canEditDelete || canModerate) && (
                  <button
                    onClick={() => onDelete(message._id)}
                    className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Delete</span>
                  </button>
                )}
                
                {!isOwnMessage && (
                  <button
                    onClick={() => onFlag(message._id, 'Inappropriate content')}
                    className="w-full px-3 py-2 text-left text-sm text-orange-600 hover:bg-orange-50 flex items-center space-x-2"
                  >
                    <Flag className="w-4 h-4" />
                    <span>Flag</span>
                  </button>
                )}
                
                {canModerate && !isOwnMessage && (
                  <>
                    <div className="border-t border-gray-100 my-1" />
                    <button
                      onClick={() => onModerateUser(message.userId, 'muteUser', 5 * 60 * 1000, 'Inappropriate message')}
                      className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                    >
                      <Clock className="w-4 h-4" />
                      <span>Timeout 5m</span>
                    </button>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}