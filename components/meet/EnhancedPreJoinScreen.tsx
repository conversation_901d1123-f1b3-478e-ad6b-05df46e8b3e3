"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Mic, MicOff, Video, VideoOff, Settings, Users, Clock } from 'lucide-react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { WaitingRoomNotification } from './WaitingRoom';

interface EnhancedPreJoinScreenProps {
  roomName: string;
  userName: string;
  onJoin: (displayName: string, audioEnabled: boolean, videoEnabled: boolean) => void;
  isLoading?: boolean;
}

export function EnhancedPreJoinScreen({ 
  roomName, 
  userName, 
  onJoin, 
  isLoading = false 
}: EnhancedPreJoinScreenProps) {
  const [displayName, setDisplayName] = useState(userName);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [videoEnabled, setVideoEnabled] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [joinStatus, setJoinStatus] = useState<'pre-join' | 'joining' | 'waiting' | 'admitted' | 'denied'>('pre-join');
  const [stream, setStream] = useState<MediaStream | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Get room data to check waiting room settings
  const roomData = useQuery(api.rooms.getByName, { name: roomName });
  const joinWaitingRoom = useMutation(api.rooms.joinWaitingRoom);

  // Video preview setup
  useEffect(() => {
    if (videoEnabled) {
      startPreview();
    } else {
      stopPreview();
    }

    return () => {
      stopPreview();
    };
  }, [videoEnabled]);

  const startPreview = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: videoEnabled,
        audio: audioEnabled
      });
      
      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (error) {
      console.error('Error accessing media devices:', error);
    }
  };

  const stopPreview = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
  };

  const handleJoin = async () => {
    if (!displayName.trim() || !roomData) return;
    
    setJoinStatus('joining');
    
    try {
      // If waiting room is enabled, join waiting room first
      if (roomData.settings?.waitingRoom) {
        await joinWaitingRoom({
          roomId: roomData._id,
        });
        setJoinStatus('waiting');
      } else {
        // Join directly
        stopPreview();
        onJoin(displayName.trim(), audioEnabled, videoEnabled);
        setJoinStatus('admitted');
      }
    } catch (error) {
      console.error('Failed to join:', error);
      setJoinStatus('pre-join');
    }
  };

  // Check participant status if in waiting room
  const participantStatus = useQuery(
    api.rooms.getParticipantStatus,
    joinStatus === 'waiting' && roomData ? { roomId: roomData._id } : "skip"
  );

  // Handle status changes
  useEffect(() => {
    if (participantStatus) {
      if (participantStatus.status === 'active') {
        setJoinStatus('admitted');
        stopPreview();
        onJoin(displayName.trim(), audioEnabled, videoEnabled);
      } else if (participantStatus.status === 'denied') {
        setJoinStatus('denied');
      }
    }
  }, [participantStatus, displayName, audioEnabled, videoEnabled, onJoin]);

  // Show waiting room notification if waiting
  if (joinStatus === 'waiting') {
    return <WaitingRoomNotification roomName={roomName} />;
  }

  // Show denied message
  if (joinStatus === 'denied') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Users className="w-8 h-8 text-red-600" />
          </div>
          
          <h1 className="text-2xl font-semibold text-gray-900 mb-4">Entry Denied</h1>
          
          <p className="text-gray-600 mb-6">
            The host has denied your request to join "{roomName}". Please contact the meeting organizer if you believe this is an error.
          </p>
          
          <button
            onClick={() => setJoinStatus('pre-join')}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Users className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">Ready to join?</h1>
          <p className="text-gray-600">Meeting: {roomName}</p>
          
          {/* Waiting Room Notice */}
          {roomData?.settings?.waitingRoom && (
            <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <div className="flex items-center space-x-2 text-orange-700">
                <Clock className="w-4 h-4" />
                <span className="text-sm">This meeting has a waiting room enabled</span>
              </div>
            </div>
          )}
        </div>

        {/* Display Name Input */}
        <div className="mb-6">
          <label htmlFor="displayName" className="block text-sm font-medium text-gray-700 mb-2">
            Your name
          </label>
          <input
            type="text"
            id="displayName"
            value={displayName}
            onChange={(e) => setDisplayName(e.target.value)}
            placeholder="Enter your name"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            onKeyPress={(e) => e.key === 'Enter' && handleJoin()}
          />
        </div>

        {/* Video Preview */}
        <div className="relative bg-gray-900 rounded-lg overflow-hidden mb-6 aspect-video">
          {videoEnabled ? (
            <video
              ref={videoRef}
              autoPlay
              muted
              playsInline
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center">
                <span className="text-white text-xl font-semibold">
                  {(displayName || userName || 'A')[0].toUpperCase()}
                </span>
              </div>
            </div>
          )}
          
          {/* Video Controls */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3">
            <button
              onClick={() => setVideoEnabled(!videoEnabled)}
              className={`p-3 rounded-full transition-colors ${
                videoEnabled 
                  ? 'bg-gray-600 hover:bg-gray-700' 
                  : 'bg-red-600 hover:bg-red-700'
              }`}
              title={videoEnabled ? 'Turn off camera' : 'Turn on camera'}
            >
              {videoEnabled ? (
                <Video className="w-5 h-5 text-white" />
              ) : (
                <VideoOff className="w-5 h-5 text-white" />
              )}
            </button>
            
            <button
              onClick={() => setAudioEnabled(!audioEnabled)}
              className={`p-3 rounded-full transition-colors ${
                audioEnabled 
                  ? 'bg-gray-600 hover:bg-gray-700' 
                  : 'bg-red-600 hover:bg-red-700'
              }`}
              title={audioEnabled ? 'Mute microphone' : 'Unmute microphone'}
            >
              {audioEnabled ? (
                <Mic className="w-5 h-5 text-white" />
              ) : (
                <MicOff className="w-5 h-5 text-white" />
              )}
            </button>

            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-3 rounded-full bg-gray-600 hover:bg-gray-700 transition-colors"
              title="Settings"
            >
              <Settings className="w-5 h-5 text-white" />
            </button>
          </div>
        </div>

        {/* Join Button */}
        <button
          onClick={handleJoin}
          disabled={!displayName.trim() || isLoading || joinStatus === 'joining'}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {joinStatus === 'joining' ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Joining...</span>
            </div>
          ) : roomData?.settings?.waitingRoom ? (
            'Request to Join'
          ) : (
            'Join Meeting'
          )}
        </button>

        {/* Settings Panel */}
        {showSettings && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-3">Audio & Video Settings</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-gray-700 mb-1">Camera</label>
                <select className="w-full text-sm border border-gray-300 rounded px-2 py-1">
                  <option>Default Camera</option>
                </select>
              </div>
              <div>
                <label className="block text-sm text-gray-700 mb-1">Microphone</label>
                <select className="w-full text-sm border border-gray-300 rounded px-2 py-1">
                  <option>Default Microphone</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
