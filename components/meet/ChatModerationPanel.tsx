"use client";

import React, { useState } from 'react';
import {
  Shield,
  AlertTriangle,
  Clock,
  Ban,
  MessageSquare,
  Eye,
  UserX,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  MoreHorizontal,
  Trash2,
  Flag,
  User
} from 'lucide-react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';

interface ChatModerationPanelProps {
  roomId: Id<"rooms">;
  onClose: () => void;
}

interface ModerationAction {
  _id: string;
  userId: string;
  moderatorId: string;
  action: string;
  reason?: string;
  duration?: number;
  timestamp: number;
  messageId?: Id<"chatMessages">;
}

interface ChatUserStatus {
  _id: string;
  userId: string;
  canSendMessages: boolean;
  canSendFiles: boolean;
  canReact: boolean;
  isMuted: boolean;
  mutedUntil?: number;
  isBanned: boolean;
  bannedUntil?: number;
  messageCount: number;
  lastMessageAt?: number;
}

export function ChatModerationPanel({ roomId, onClose }: ChatModerationPanelProps) {
  const [activeTab, setActiveTab] = useState<'actions' | 'users' | 'flagged'>('actions');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [bulkAction, setBulkAction] = useState<string>('');

  // Queries
  const moderationActions = useQuery(api.chat.getModerationActions, { roomId, limit: 50 }) as ModerationAction[] | undefined;
  const chatUsers = useQuery(api.chat.getChatUsers, { roomId }) as ChatUserStatus[] | undefined;
  const flaggedMessages = useQuery(api.chat.getFlaggedMessages, { roomId }) as any[] | undefined;

  // Mutations
  const moderateUserMutation = useMutation(api.chat.moderateUser);
  const deleteMessageMutation = useMutation(api.chat.deleteMessage);
  const unflagMessageMutation = useMutation(api.chat.unflagMessage);

  const handleModerateUser = async (userId: string, action: string, duration?: number, reason?: string) => {
    try {
      await moderateUserMutation({
        roomId,
        userId,
        action: action as any,
        duration,
        reason,
      });
    } catch (error) {
      console.error('Failed to moderate user:', error);
    }
  };

  const handleBulkAction = async () => {
    if (!bulkAction || selectedUsers.size === 0) return;

    for (const userId of Array.from(selectedUsers)) {
      await handleModerateUser(userId, bulkAction, undefined, 'Bulk moderation action');
    }
    
    setSelectedUsers(new Set());
    setBulkAction('');
  };

  const handleDeleteMessage = async (messageId: Id<"chatMessages">) => {
    try {
      await deleteMessageMutation({ messageId, reason: 'Inappropriate content' });
    } catch (error) {
      console.error('Failed to delete message:', error);
    }
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d`;
    if (hours > 0) return `${hours}h`;
    return `${minutes}m`;
  };

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const filteredActions = moderationActions?.filter(action =>
    action.userId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    action.action.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const filteredUsers = chatUsers?.filter(user =>
    user.userId.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <Shield className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Chat Moderation</h2>
              <p className="text-sm text-gray-500">Manage chat messages and user permissions</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <XCircle className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex items-center space-x-1 px-6 py-4 border-b border-gray-200">
          <button
            onClick={() => setActiveTab('actions')}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'actions' 
                ? 'bg-blue-100 text-blue-700' 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            Recent Actions ({moderationActions?.length || 0})
          </button>
          <button
            onClick={() => setActiveTab('users')}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'users' 
                ? 'bg-blue-100 text-blue-700' 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            Chat Users ({chatUsers?.length || 0})
          </button>
          <button
            onClick={() => setActiveTab('flagged')}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeTab === 'flagged' 
                ? 'bg-red-100 text-red-700' 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            Flagged Messages ({flaggedMessages?.length || 0})
          </button>
        </div>

        {/* Search and Bulk Actions */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                />
              </div>
            </div>

            {activeTab === 'users' && selectedUsers.size > 0 && (
              <div className="flex items-center space-x-2">
                <select
                  value={bulkAction}
                  onChange={(e) => setBulkAction(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Bulk action...</option>
                  <option value="muteUser">Mute users</option>
                  <option value="unmuteUser">Unmute users</option>
                  <option value="timeoutUser">Timeout 5 minutes</option>
                  <option value="banUser">Ban users</option>
                </select>
                <button
                  onClick={handleBulkAction}
                  disabled={!bulkAction}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Apply to {selectedUsers.size} users
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          {activeTab === 'actions' && (
            <div className="p-6">
              <div className="space-y-4">
                {filteredActions.map((action) => (
                  <div key={action._id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-lg ${getActionIconBg(action.action)}`}>
                        {getActionIcon(action.action)}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {getActionDescription(action)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatTimeAgo(action.timestamp)}
                          {action.reason && ` • ${action.reason}`}
                        </div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {action.duration && `Duration: ${formatDuration(action.duration)}`}
                    </div>
                  </div>
                ))}
                {filteredActions.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Shield className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No moderation actions yet</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'users' && (
            <div className="p-6">
              <div className="space-y-4">
                {filteredUsers.map((user) => (
                  <div key={user._id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <input
                        type="checkbox"
                        checked={selectedUsers.has(user.userId)}
                        onChange={(e) => {
                          const newSelected = new Set(selectedUsers);
                          if (e.target.checked) {
                            newSelected.add(user.userId);
                          } else {
                            newSelected.delete(user.userId);
                          }
                          setSelectedUsers(newSelected);
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
                        {user.userId[0]?.toUpperCase() || 'U'}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{user.userId}</div>
                        <div className="text-sm text-gray-500">
                          {user.messageCount} messages
                          {user.lastMessageAt && ` • Last: ${formatTimeAgo(user.lastMessageAt)}`}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {/* User Status Badges */}
                      {user.isMuted && (
                        <span className="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">
                          Muted {user.mutedUntil && `until ${new Date(user.mutedUntil).toLocaleTimeString()}`}
                        </span>
                      )}
                      {user.isBanned && (
                        <span className="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">
                          Banned {user.bannedUntil && `until ${new Date(user.bannedUntil).toLocaleTimeString()}`}
                        </span>
                      )}
                      
                      {/* Action Buttons */}
                      <div className="flex items-center space-x-1">
                        {user.isMuted ? (
                          <button
                            onClick={() => handleModerateUser(user.userId, 'unmuteUser')}
                            className="p-2 text-green-600 hover:bg-green-100 rounded-lg"
                            title="Unmute user"
                          >
                            <CheckCircle className="w-4 h-4" />
                          </button>
                        ) : (
                          <button
                            onClick={() => handleModerateUser(user.userId, 'muteUser')}
                            className="p-2 text-red-600 hover:bg-red-100 rounded-lg"
                            title="Mute user"
                          >
                            <UserX className="w-4 h-4" />
                          </button>
                        )}
                        
                        <button
                          onClick={() => handleModerateUser(user.userId, 'timeoutUser', 5 * 60 * 1000)}
                          className="p-2 text-orange-600 hover:bg-orange-100 rounded-lg"
                          title="Timeout 5 minutes"
                        >
                          <Clock className="w-4 h-4" />
                        </button>
                        
                        <button
                          onClick={() => handleModerateUser(user.userId, 'banUser')}
                          className="p-2 text-red-600 hover:bg-red-100 rounded-lg"
                          title="Ban user"
                        >
                          <Ban className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
                {filteredUsers.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <User className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No chat users found</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'flagged' && (
            <div className="p-6">
              <div className="space-y-4">
                {flaggedMessages?.map((message) => (
                  <div key={message._id} className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Flag className="w-4 h-4 text-red-500" />
                          <span className="font-medium text-gray-900">{message.userName}</span>
                          <span className="text-sm text-gray-500">
                            {formatTimeAgo(message.timestamp)}
                          </span>
                        </div>
                        <div className="text-gray-800 mb-2">{message.message}</div>
                        <div className="text-sm text-red-600">
                          Flagged: {message.flaggedReason}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleDeleteMessage(message._id)}
                          className="p-2 text-red-600 hover:bg-red-100 rounded-lg"
                          title="Delete message"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => unflagMessageMutation({ messageId: message._id })}
                          className="p-2 text-green-600 hover:bg-green-100 rounded-lg"
                          title="Unflag message"
                        >
                          <CheckCircle className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
                {(!flaggedMessages || flaggedMessages.length === 0) && (
                  <div className="text-center py-8 text-gray-500">
                    <Flag className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No flagged messages</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Helper functions
function getActionIcon(action: string) {
  switch (action) {
    case 'muteUser':
      return <UserX className="w-4 h-4 text-red-600" />;
    case 'unmuteUser':
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    case 'timeoutUser':
      return <Clock className="w-4 h-4 text-orange-600" />;
    case 'banUser':
      return <Ban className="w-4 h-4 text-red-600" />;
    case 'deleteMessage':
      return <Trash2 className="w-4 h-4 text-red-600" />;
    case 'flagMessage':
      return <Flag className="w-4 h-4 text-orange-600" />;
    default:
      return <Shield className="w-4 h-4 text-gray-600" />;
  }
}

function getActionIconBg(action: string) {
  switch (action) {
    case 'muteUser':
    case 'banUser':
    case 'deleteMessage':
      return 'bg-red-100';
    case 'unmuteUser':
      return 'bg-green-100';
    case 'timeoutUser':
    case 'flagMessage':
      return 'bg-orange-100';
    default:
      return 'bg-gray-100';
  }
}

function getActionDescription(action: any) {
  switch (action.action) {
    case 'muteUser':
      return `Muted ${action.userId}`;
    case 'unmuteUser':
      return `Unmuted ${action.userId}`;
    case 'timeoutUser':
      return `Timed out ${action.userId}`;
    case 'banUser':
      return `Banned ${action.userId}`;
    case 'deleteMessage':
      return `Deleted message from ${action.userId}`;
    case 'flagMessage':
      return `Flagged message from ${action.userId}`;
    default:
      return `${action.action} - ${action.userId}`;
  }
}