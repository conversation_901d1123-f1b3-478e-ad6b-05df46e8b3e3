"use client";

import React, { useState } from 'react';
import { 
  Mic, 
  MicOff, 
  Video, 
  VideoOff, 
  Monitor,
  PhoneOff,
  Hand,
  Settings,
  MoreVertical,
  Users,
  MessageSquare,
  Circle,
  Pause,
  Play,
  StopCircle,
  Camera,
  Headphones,
  Volume2,
  Shield
} from 'lucide-react';
import { useLiveKitControls } from '../../hooks/useLiveKitControls';
import { RecordingControls } from './RecordingControls';
import { ChatModerationPanel } from './ChatModerationPanel';
import { Id } from '../../convex/_generated/dataModel';

interface MeetingControlsProps {
  onToggleChat: () => void;
  onToggleParticipants: () => void;
  onLeaveMeeting: () => void;
  canModerate?: boolean;
  participantCount: number;
  isChatOpen: boolean;
  isParticipantsOpen: boolean;
  roomId?: Id<"rooms">;
  roomName?: string;
}

export function MeetingControls({
  onToggleChat,
  onToggleParticipants,
  onLeaveMeeting,
  canModerate = false,
  participantCount,
  isChatOpen,
  isParticipantsOpen,
  roomId,
  roomName
}: MeetingControlsProps) {
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const [showDeviceSettings, setShowDeviceSettings] = useState(false);
  const [showModerationPanel, setShowModerationPanel] = useState(false);
  
  const {
    isMuted,
    isVideoOff,
    isScreenSharing,
    isRecording,
    isLoading,
    error,
    toggleMute,
    toggleVideo,
    toggleScreenShare,
    toggleRecording,
    clearError
  } = useLiveKitControls();

  const handleRaiseHand = () => {
    // TODO: Implement hand raise functionality
    console.log('Hand raised');
  };

  const handleDeviceSettings = () => {
    setShowDeviceSettings(!showDeviceSettings);
  };

  return (
    <div className="relative">
      {/* Main Control Bar */}
      <div className="bg-white border-t border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          {/* Left Controls - Media */}
          <div className="flex items-center space-x-3">
            {/* Microphone */}
            <div className="relative">
              <button
                onClick={toggleMute}
                disabled={isLoading.audio}
                className={`p-3 rounded-full transition-all duration-200 ${
                  isMuted 
                    ? 'bg-red-500 text-white hover:bg-red-600 shadow-lg' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                } ${isLoading.audio ? 'opacity-50 cursor-not-allowed' : ''}`}
                title={isMuted ? 'Unmute microphone' : 'Mute microphone'}
              >
                {isMuted ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
              </button>
              
              {/* Microphone dropdown */}
              <button
                onClick={handleDeviceSettings}
                className="absolute -top-1 -right-1 w-6 h-6 bg-gray-600 text-white rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors"
                title="Audio settings"
              >
                <Settings className="w-3 h-3" />
              </button>
            </div>

            {/* Camera */}
            <div className="relative">
              <button
                onClick={toggleVideo}
                disabled={isLoading.video}
                className={`p-3 rounded-full transition-all duration-200 ${
                  isVideoOff 
                    ? 'bg-red-500 text-white hover:bg-red-600 shadow-lg' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                } ${isLoading.video ? 'opacity-50 cursor-not-allowed' : ''}`}
                title={isVideoOff ? 'Turn on camera' : 'Turn off camera'}
              >
                {isVideoOff ? <VideoOff className="w-5 h-5" /> : <Video className="w-5 h-5" />}
              </button>
              
              {/* Camera dropdown */}
              <button
                onClick={handleDeviceSettings}
                className="absolute -top-1 -right-1 w-6 h-6 bg-gray-600 text-white rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors"
                title="Video settings"
              >
                <Camera className="w-3 h-3" />
              </button>
            </div>

            {/* Screen Share */}
            <button
              onClick={toggleScreenShare}
              disabled={isLoading.screenShare}
              className={`p-3 rounded-full transition-all duration-200 ${
                isScreenSharing 
                  ? 'bg-blue-500 text-white hover:bg-blue-600 shadow-lg' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              } ${isLoading.screenShare ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={isScreenSharing ? 'Stop sharing screen' : 'Share screen'}
            >
              <Monitor className="w-5 h-5" />
            </button>

            {/* Hand Raise */}
            <button
              onClick={handleRaiseHand}
              className="p-3 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
              title="Raise hand"
            >
              <Hand className="w-5 h-5" />
            </button>
          </div>

          {/* Center - Meeting Info */}
          <div className="flex items-center space-x-4">
            {isRecording && (
              <div className="flex items-center space-x-2 px-3 py-2 bg-red-100 text-red-700 rounded-lg">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium">Recording</span>
              </div>
            )}
          </div>

          {/* Right Controls - Actions */}
          <div className="flex items-center space-x-3">
            {/* Participants */}
            <button
              onClick={onToggleParticipants}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                isParticipantsOpen 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              title="Participants"
            >
              <Users className="w-5 h-5" />
              <span className="text-sm font-medium">{participantCount}</span>
            </button>

            {/* Chat */}
            <button
              onClick={onToggleChat}
              className={`p-3 rounded-full transition-colors ${
                isChatOpen 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              title="Chat"
            >
              <MessageSquare className="w-5 h-5" />
            </button>

            {/* Recording Controls (Host only) */}
            {canModerate && roomId && roomName && (
              <RecordingControls
                roomId={roomId}
                roomName={roomName}
                isHost={canModerate}
              />
            )}

            {/* Chat Moderation (Host only) */}
            {canModerate && roomId && (
              <button
                onClick={() => setShowModerationPanel(true)}
                className="p-3 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                title="Chat moderation"
              >
                <Shield className="w-5 h-5" />
              </button>
            )}

            {/* More Options */}
            <div className="relative">
              <button
                onClick={() => setShowMoreOptions(!showMoreOptions)}
                className="p-3 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                title="More options"
              >
                <MoreVertical className="w-5 h-5" />
              </button>

              {showMoreOptions && (
                <div className="absolute bottom-full right-0 mb-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10">
                  <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2">
                    <Settings className="w-4 h-4" />
                    <span>Settings</span>
                  </button>
                  <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2">
                    <Headphones className="w-4 h-4" />
                    <span>Audio & Video</span>
                  </button>
                  <div className="border-t border-gray-100 my-1" />
                  <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2">
                    <Volume2 className="w-4 h-4" />
                    <span>Speaker Test</span>
                  </button>
                </div>
              )}
            </div>

            {/* Leave Meeting */}
            <button
              onClick={onLeaveMeeting}
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-medium"
              title="Leave meeting"
            >
              <div className="flex items-center space-x-2">
                <PhoneOff className="w-4 h-4" />
                <span>Leave</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm">{error}</span>
            <button onClick={clearError} className="ml-2 text-red-500 hover:text-red-700">
              ×
            </button>
          </div>
        </div>
      )}

      {/* Device Settings Modal */}
      {showDeviceSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-90vw">
            <h3 className="text-lg font-semibold mb-4">Audio & Video Settings</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Camera</label>
                <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                  <option>Default Camera</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Microphone</label>
                <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                  <option>Default Microphone</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Speaker</label>
                <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                  <option>Default Speaker</option>
                </select>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowDeviceSettings(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowDeviceSettings(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Chat Moderation Panel */}
      {showModerationPanel && roomId && (
        <ChatModerationPanel
          roomId={roomId}
          onClose={() => setShowModerationPanel(false)}
        />
      )}
    </div>
  );
}
