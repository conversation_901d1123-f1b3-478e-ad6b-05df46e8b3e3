"use client";

import { useParams } from 'next/navigation';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { useUser } from '@clerk/nextjs';
import { VideoConference } from '../../../components/meet/VideoConference';
import { PreJoinScreen } from '../../../components/meet/PreJoinScreen';
import { ErrorBoundary } from '../../../components/ErrorBoundary';
import { useState, useEffect } from 'react';

export default function RoomPage() {
  const { roomName } = useParams();
  const { user } = useUser();
  const [hasJoined, setHasJoined] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [userName, setUserName] = useState('');

  const room = useQuery(api.rooms.getRoom, { name: roomName as string });
  const createRoom = useMutation(api.rooms.create);
  const createToken = useAction(api.livekit.createToken);

  useEffect(() => {
    if (user) {
      setUserName(user.firstName || user.username || 'Anonymous');
    }
  }, [user]);

  const handleJoinRoom = async (displayName: string) => {
    try {
      // Create room if it doesn't exist
      if (!room) {
        await createRoom({ 
          name: roomName as string,
          title: `Meeting: ${roomName}`,
          description: `Video meeting room for ${roomName}`
        });
      }

      // Generate LiveKit token
      const liveKitToken = await createToken({
        roomName: roomName as string,
        participantName: displayName || userName || 'Anonymous',
      });

      setToken(liveKitToken);
      setHasJoined(true);
    } catch (error) {
      console.error('Failed to join room:', error);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Please sign in to join the meeting</h1>
          <a 
            href="/sign-in" 
            className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Sign In
          </a>
        </div>
      </div>
    );
  }

  if (!hasJoined || !token) {
    return (
      <PreJoinScreen
        roomName={roomName as string}
        userName={userName}
        onJoin={handleJoinRoom}
      />
    );
  }

  return (
    <ErrorBoundary>
      <VideoConference
        roomName={roomName as string}
        token={token}
        userName={userName}
      />
    </ErrorBoundary>
  );
}