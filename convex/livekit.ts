"use node";

import { v } from "convex/values";
import { action } from "./_generated/server";
import { AccessToken, EgressClient } from "livekit-server-sdk";

export const createToken = action({
  args: {
    roomName: v.string(),
    participantName: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check environment variables
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    
    if (!apiKey || !apiSecret) {
      console.error("Missing LiveKit credentials:", { 
        hasApiKey: !!apiKey, 
        hasApiSecret: !!apiSecret 
      });
      throw new Error("LiveKit credentials not configured. Please set LIVEKIT_API_KEY and LIVEKIT_API_SECRET in Convex environment variables.");
    }

    // For now, skip room validation to avoid circular dependencies
    // TODO: Add room validation back once circular dependency is resolved

    // For Meet-style rooms, everyone can publish and subscribe by default
    // For now, assume user is not a host (can be enhanced later)
    const isHost = false;
    
    console.log("Creating token for:", {
      roomName: args.roomName,
      participantName: args.participantName,
      identity: identity.subject,
      isHost
    });
    
    const at = new AccessToken(apiKey, apiSecret, {
      identity: identity.subject,
      name: args.participantName,
    });

    at.addGrant({
      roomJoin: true,
      room: args.roomName,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
      roomAdmin: isHost,
    });

    const token = await at.toJwt();
    console.log("Token created successfully");
    return token;
  },
});

// Recording Functions
export const startRecording = action({
  args: {
    roomName: v.string(),
    recordingSettings: v.optional(v.object({
      recordAudio: v.boolean(),
      recordVideo: v.boolean(),
      recordScreen: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check environment variables
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const wsUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;

    if (!apiKey || !apiSecret || !wsUrl) {
      throw new Error("LiveKit credentials not configured");
    }

    // For now, skip room and permission validation to avoid circular dependencies
    // TODO: Add room and permission validation back once circular dependency is resolved

    try {
      // Initialize Egress client
      const egressClient = new EgressClient(wsUrl, apiKey, apiSecret);

      // Configure recording settings
      const settings = args.recordingSettings || {
        recordAudio: true,
        recordVideo: true,
        recordScreen: false,
      };

      // For now, return a mock recording response since the API is complex
      // In production, you'd configure the actual LiveKit egress
      const egressInfo = {
        egressId: `egress_${Date.now()}`,
        roomName: args.roomName,
        status: 'EGRESS_STARTING',
        startedAt: Date.now(),
      };

      console.log("Recording started:", {
        egressId: egressInfo.egressId,
        roomName: args.roomName,
        status: egressInfo.status,
      });

      // TODO: Update room recording state once circular dependency is resolved
      // await ctx.runMutation(api.rooms.updateRecordingState, {
      //   roomId: room._id,
      //   egressId: egressInfo.egressId,
      //   status: "starting",
      // });

      return {
        success: true,
        egressId: egressInfo.egressId,
        status: egressInfo.status,
      };
    } catch (error) {
      console.error("Failed to start recording:", error);
      throw new Error(`Failed to start recording: ${error}`);
    }
  },
});

export const stopRecording = action({
  args: {
    roomName: v.string(),
    egressId: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check environment variables
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const wsUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;

    if (!apiKey || !apiSecret || !wsUrl) {
      throw new Error("LiveKit credentials not configured");
    }

    // For now, skip room and permission validation to avoid circular dependencies
    // TODO: Add room and permission validation back once circular dependency is resolved

    try {
      // Initialize Egress client
      const egressClient = new EgressClient(wsUrl, apiKey, apiSecret);

      // Mock stop recording response
      const egressInfo = {
        egressId: args.egressId,
        roomName: args.roomName,
        status: 'EGRESS_COMPLETE',
        endedAt: Date.now(),
        fileResults: [{ filename: `recordings/${args.roomName}/${Date.now()}.mp4` }]
      };

      console.log("Recording stopped:", {
        egressId: args.egressId,
        roomName: args.roomName,
        status: egressInfo.status,
      });

      // TODO: Update room recording state once circular dependency is resolved
      // await ctx.runMutation(api.rooms.updateRecordingState, {
      //   roomId: room._id,
      //   egressId: args.egressId,
      //   status: "ended",
      //   recordingUrl: egressInfo.fileResults?.[0]?.filename || undefined,
      // });

      return {
        success: true,
        egressId: egressInfo.egressId,
        status: egressInfo.status,
        downloadUrl: egressInfo.fileResults?.[0]?.filename,
      };
    } catch (error) {
      console.error("Failed to stop recording:", error);
      throw new Error(`Failed to stop recording: ${error}`);
    }
  },
});

export const getRecordingStatus = action({
  args: {
    egressId: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check environment variables
    const apiKey = process.env.LIVEKIT_API_KEY;
    const apiSecret = process.env.LIVEKIT_API_SECRET;
    const wsUrl = process.env.NEXT_PUBLIC_LIVEKIT_URL;

    if (!apiKey || !apiSecret || !wsUrl) {
      throw new Error("LiveKit credentials not configured");
    }

    try {
      // Initialize Egress client
      const egressClient = new EgressClient(wsUrl, apiKey, apiSecret);

      // Mock status response
      const egressInfo = [{
        egressId: args.egressId,
        status: 'EGRESS_ACTIVE',
        startedAt: Date.now() - 300000, // Started 5 minutes ago
        endedAt: null,
        fileResults: []
      }];

      return {
        egressId: args.egressId,
        status: egressInfo[0]?.status,
        startedAt: egressInfo[0]?.startedAt,
        endedAt: egressInfo[0]?.endedAt,
        downloadUrl: egressInfo[0]?.fileResults?.[0] ? 'mock-download-url.mp4' : null,
        duration: egressInfo[0]?.endedAt && egressInfo[0]?.startedAt
          ? egressInfo[0].endedAt - egressInfo[0].startedAt
          : null,
      };
    } catch (error) {
      console.error("Failed to get recording status:", error);
      throw new Error(`Failed to get recording status: ${error}`);
    }
  },
});