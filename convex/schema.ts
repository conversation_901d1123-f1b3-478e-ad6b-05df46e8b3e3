import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  rooms: defineTable({
    name: v.string(),
    title: v.string(),
    description: v.optional(v.string()),
    hostId: v.string(),
    isActive: v.boolean(),
    maxParticipants: v.optional(v.number()),
    settings: v.optional(v.object({
      allowScreenShare: v.boolean(),
      allowChat: v.boolean(),
      requireAuth: v.boolean(),
      muteOnEntry: v.optional(v.boolean()),
      videoOnEntry: v.optional(v.boolean()),
      recordingEnabled: v.optional(v.boolean()),
      waitingRoom: v.optional(v.boolean()),
      moderatorApproval: v.optional(v.boolean()),
      handRaiseEnabled: v.optional(v.boolean()),
    })),
    layout: v.optional(v.union(
      v.literal("grid"),
      v.literal("speaker"),
      v.literal("sidebar"),
      v.literal("floating")
    )),
    recordingSettings: v.optional(v.object({
      autoRecord: v.boolean(),
      recordAudio: v.boolean(),
      recordVideo: v.boolean(),
      recordScreen: v.boolean(),
    })),
    // Recording metadata
    currentRecording: v.optional(v.object({
      egressId: v.string(),
      startedAt: v.number(),
      recordingUrl: v.optional(v.string()),
      status: v.union(
        v.literal("starting"),
        v.literal("active"),
        v.literal("ending"),
        v.literal("ended"),
        v.literal("failed")
      ),
    })),
  }).index("by_name", ["name"]).index("by_host", ["hostId"]),
  
  streams: defineTable({
    title: v.string(),
    description: v.string(),
    hostId: v.string(),
    isLive: v.boolean(),
    isChatEnabled: v.boolean(),
    isChatDelayed: v.boolean(),
    isChatFollowersOnly: v.boolean(),
    maxParticipants: v.optional(v.number()),
    streamKey: v.optional(v.string()),
  }).index("by_host", ["hostId"]),
  
  users: defineTable({
    userId: v.string(),
    globalRole: v.union(v.literal("master"), v.literal("admin"), v.literal("user")),
    username: v.string(),
    email: v.string(),
    isBanned: v.boolean(),
  }).index("by_user_id", ["userId"]),
  
  streamParticipants: defineTable({
    streamId: v.id("streams"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("guest"),
      v.literal("viewer")
    ),
    joinedAt: v.number(),
    isActive: v.boolean(),
  })
    .index("by_stream", ["streamId"])
    .index("by_user_stream", ["userId", "streamId"]),
  
  moderationLogs: defineTable({
    streamId: v.id("streams"),
    moderatorId: v.string(),
    targetUserId: v.string(),
    action: v.union(
      v.literal("mute"),
      v.literal("unmute"),
      v.literal("timeout"),
      v.literal("kick"),
      v.literal("ban")
    ),
    reason: v.optional(v.string()),
    duration: v.optional(v.number()),
    timestamp: v.number(),
  })
    .index("by_stream", ["streamId"])
    .index("by_target", ["targetUserId"]),

  roomParticipants: defineTable({
    roomId: v.id("rooms"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("speaker"),
      v.literal("attendee")
    ),
    permissions: v.object({
      canSpeak: v.boolean(),
      canVideo: v.boolean(),
      canScreenShare: v.boolean(),
      canChat: v.boolean(),
      canInvite: v.boolean(),
      canMute: v.boolean(),
      canKick: v.boolean(),
    }),
    status: v.union(
      v.literal("waiting"),
      v.literal("approved"),
      v.literal("denied"),
      v.literal("active"),
      v.literal("muted"),
      v.literal("kicked")
    ),
    handRaised: v.boolean(),
    joinedAt: v.number(),
    lastSeen: v.number(),
  })
    .index("by_room", ["roomId"])
    .index("by_user_room", ["userId", "roomId"]),

  roomActions: defineTable({
    roomId: v.id("rooms"),
    performedBy: v.string(),
    targetUserId: v.optional(v.string()),
    action: v.union(
      v.literal("mute"),
      v.literal("unmute"),
      v.literal("muteAll"),
      v.literal("unmuteAll"),
      v.literal("kick"),
      v.literal("promote"),
      v.literal("demote"),
      v.literal("allowSpeak"),
      v.literal("denySpeak"),
      v.literal("startRecording"),
      v.literal("stopRecording"),
      v.literal("changeLayout"),
      v.literal("lockRoom"),
      v.literal("unlockRoom"),
      v.literal("joinRoom"),
      v.literal("joinWaitingRoom"),
      v.literal("admitParticipant"),
      v.literal("denyParticipant"),
      v.literal("admitAllWaiting")
    ),
    metadata: v.optional(v.any()),
    timestamp: v.number(),
  }).index("by_room", ["roomId"]),

  // Enhanced chat system
  chatMessages: defineTable({
    roomId: v.id("rooms"),
    userId: v.string(),
    userName: v.string(),
    message: v.string(),
    messageType: v.union(
      v.literal("text"),
      v.literal("file"),
      v.literal("image"),
      v.literal("system"),
      v.literal("announcement")
    ),
    
    // File sharing
    fileData: v.optional(v.object({
      fileName: v.string(),
      fileSize: v.number(),
      fileType: v.string(),
      fileUrl: v.string(),
      thumbnailUrl: v.optional(v.string()),
    })),
    
    // Threading and replies
    replyToMessageId: v.optional(v.id("chatMessages")),
    threadId: v.optional(v.string()),
    
    // Message status
    isEdited: v.boolean(),
    editedAt: v.optional(v.number()),
    isDeleted: v.boolean(),
    deletedAt: v.optional(v.number()),
    deletedBy: v.optional(v.string()),
    
    // Moderation
    isFlagged: v.boolean(),
    flaggedBy: v.optional(v.string()),
    flaggedReason: v.optional(v.string()),
    isModerated: v.boolean(),
    moderatedBy: v.optional(v.string()),
    moderatedAt: v.optional(v.number()),
    
    timestamp: v.number(),
  })
    .index("by_room", ["roomId"])
    .index("by_user", ["userId"])
    .index("by_thread", ["threadId"])
    .index("by_reply", ["replyToMessageId"]),

  // Chat reactions
  chatReactions: defineTable({
    messageId: v.id("chatMessages"),
    userId: v.string(),
    userName: v.string(),
    reaction: v.string(), // emoji unicode or predefined reaction
    timestamp: v.number(),
  })
    .index("by_message", ["messageId"])
    .index("by_user_message", ["userId", "messageId"]),

  // Chat moderation actions
  chatModerationActions: defineTable({
    roomId: v.id("rooms"),
    messageId: v.optional(v.id("chatMessages")),
    userId: v.string(), // target user
    moderatorId: v.string(),
    action: v.union(
      v.literal("deleteMessage"),
      v.literal("editMessage"),
      v.literal("muteUser"),
      v.literal("unmuteUser"),
      v.literal("warnUser"),
      v.literal("timeoutUser"),
      v.literal("banUser"),
      v.literal("flagMessage"),
      v.literal("unflagMessage")
    ),
    reason: v.optional(v.string()),
    duration: v.optional(v.number()), // for timeouts
    metadata: v.optional(v.any()),
    timestamp: v.number(),
  })
    .index("by_room", ["roomId"])
    .index("by_user", ["userId"])
    .index("by_moderator", ["moderatorId"]),

  // User chat preferences and status
  chatUserStatus: defineTable({
    roomId: v.id("rooms"),
    userId: v.string(),
    
    // Chat permissions
    canSendMessages: v.boolean(),
    canSendFiles: v.boolean(),
    canReact: v.boolean(),
    canCreateThreads: v.boolean(),
    
    // Moderation status
    isMuted: v.boolean(),
    mutedUntil: v.optional(v.number()),
    mutedBy: v.optional(v.string()),
    isBanned: v.boolean(),
    bannedUntil: v.optional(v.number()),
    bannedBy: v.optional(v.string()),
    
    // User preferences
    notifications: v.object({
      mentions: v.boolean(),
      reactions: v.boolean(),
      replies: v.boolean(),
      files: v.boolean(),
    }),
    
    // Activity tracking
    lastMessageAt: v.optional(v.number()),
    messageCount: v.number(),
    
    timestamp: v.number(),
    lastUpdated: v.number(),
  })
    .index("by_room_user", ["roomId", "userId"])
    .index("by_room", ["roomId"]),
});