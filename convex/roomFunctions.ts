import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Create a new room
export const create = mutation({
  args: {
    name: v.string(),
    title: v.string(),
    description: v.optional(v.string()),
    maxParticipants: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const existingRoom = await ctx.db
      .query("rooms")
      .withIndex("by_name", (q: any) => q.eq("name", args.name))
      .first();

    if (existingRoom) {
      throw new Error("Room with this name already exists");
    }

    const roomId = await ctx.db.insert("rooms", {
      name: args.name,
      title: args.title,
      description: args.description,
      hostId: identity.subject,
      isActive: true,
      maxParticipants: args.maxParticipants || 50,
      settings: {
        allowScreenShare: true,
        allowChat: true,
        requireAuth: false,
        muteOnEntry: false,
        videoOnEntry: true,
        recordingEnabled: false,
        waitingRoom: false,
        moderatorApproval: false,
        handRaiseEnabled: true,
      },
      layout: "grid",
    });

    return roomId;
  },
});

// Get room by name
export const getByName = query({
  args: { name: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("rooms")
      .withIndex("by_name", (q: any) => q.eq("name", args.name))
      .first();
  },
});

// Get room by ID
export const get = query({
  args: { id: v.id("rooms") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Get room (alias for getByName)
export const getRoom = query({
  args: { name: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("rooms")
      .withIndex("by_name", (q: any) => q.eq("name", args.name))
      .first();
  },
});

// Get my rooms
export const getMyRooms = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("rooms")
      .withIndex("by_host", (q: any) => q.eq("hostId", identity.subject))
      .order("desc")
      .collect();
  },
});

// Join waiting room
export const joinWaitingRoom = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Check if participant already exists
    const existingParticipant = await ctx.db
      .query("roomParticipants")
      .withIndex("by_user_room", (q: any) => 
        q.eq("userId", identity.subject).eq("roomId", args.roomId))
      .first();

    if (existingParticipant) {
      if (existingParticipant.status === "waiting") {
        return existingParticipant._id;
      }
      await ctx.db.patch(existingParticipant._id, {
        status: "waiting",
        lastSeen: Date.now(),
      });
      return existingParticipant._id;
    }

    // Create new participant in waiting status
    const participantId = await ctx.db.insert("roomParticipants", {
      roomId: args.roomId,
      userId: identity.subject,
      role: "attendee",
      permissions: {
        canSpeak: false,
        canVideo: false,
        canScreenShare: false,
        canChat: true,
        canInvite: false,
        canMute: false,
        canKick: false,
      },
      status: "waiting",
      handRaised: false,
      joinedAt: Date.now(),
      lastSeen: Date.now(),
    });

    return participantId;
  },
});

// Get waiting participants
export const getWaitingParticipants = query({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("roomParticipants")
      .withIndex("by_room", (q: any) => q.eq("roomId", args.roomId))
      .filter((q: any) => q.eq(q.field("status"), "waiting"))
      .order("asc")
      .collect();
  },
});

// Admit participant
export const admitParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Check permissions
    const canAdmit = room.hostId === identity.subject;
    if (!canAdmit) {
      throw new Error("You don't have permission to admit participants");
    }

    // Find and update participant
    const participant = await ctx.db
      .query("roomParticipants")
      .withIndex("by_user_room", (q: any) => 
        q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();

    if (!participant) {
      throw new Error("Participant not found");
    }

    await ctx.db.patch(participant._id, {
      status: "active",
      permissions: {
        canSpeak: true,
        canVideo: true,
        canScreenShare: false,
        canChat: true,
        canInvite: false,
        canMute: false,
        canKick: false,
      },
    });

    return { success: true };
  },
});

// Deny participant
export const denyParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Check permissions
    const canDeny = room.hostId === identity.subject;
    if (!canDeny) {
      throw new Error("You don't have permission to deny participants");
    }

    // Find and update participant
    const participant = await ctx.db
      .query("roomParticipants")
      .withIndex("by_user_room", (q: any) => 
        q.eq("userId", args.userId).eq("roomId", args.roomId))
      .first();

    if (!participant) {
      throw new Error("Participant not found");
    }

    await ctx.db.patch(participant._id, {
      status: "denied",
    });

    return { success: true };
  },
});

// Admit all waiting participants
export const admitAllWaiting = mutation({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Check permissions
    const canAdmit = room.hostId === identity.subject;
    if (!canAdmit) {
      throw new Error("You don't have permission to admit participants");
    }

    // Get all waiting participants
    const waitingParticipants = await ctx.db
      .query("roomParticipants")
      .withIndex("by_room", (q: any) => q.eq("roomId", args.roomId))
      .filter((q: any) => q.eq(q.field("status"), "waiting"))
      .collect();

    // Update all to active
    for (const participant of waitingParticipants) {
      await ctx.db.patch(participant._id, {
        status: "active",
        permissions: {
          canSpeak: true,
          canVideo: true,
          canScreenShare: false,
          canChat: true,
          canInvite: false,
          canMute: false,
          canKick: false,
        },
      });
    }

    return { success: true, count: waitingParticipants.length };
  },
});

// Get participant status
export const getParticipantStatus = query({
  args: {
    roomId: v.id("rooms"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    return await ctx.db
      .query("roomParticipants")
      .withIndex("by_user_room", (q: any) => 
        q.eq("userId", identity.subject).eq("roomId", args.roomId))
      .first();
  },
});