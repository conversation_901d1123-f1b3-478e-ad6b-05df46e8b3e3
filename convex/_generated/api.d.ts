/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as chat from "../chat.js";
import type * as chatFunctions from "../chatFunctions.js";
import type * as livekit from "../livekit.js";
import type * as moderation from "../moderation.js";
import type * as participants from "../participants.js";
import type * as roomFunctions from "../roomFunctions.js";
import type * as rooms from "../rooms.js";
import type * as streams from "../streams.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  chat: typeof chat;
  chatFunctions: typeof chatFunctions;
  livekit: typeof livekit;
  moderation: typeof moderation;
  participants: typeof participants;
  roomFunctions: typeof roomFunctions;
  rooms: typeof rooms;
  streams: typeof streams;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
