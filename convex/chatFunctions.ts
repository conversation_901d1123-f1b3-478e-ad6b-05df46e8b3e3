import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Helper function to check moderation permissions
async function checkModerationPermissions(ctx: any, roomId: string) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Not authenticated");
  }

  // Check if user has moderation permissions
  const moderatorParticipant = await ctx.db
    .query("roomParticipants")
    .withIndex("by_user_room", (q: any) => 
      q.eq("userId", identity.subject).eq("roomId", roomId))
    .first();

  // Get room to check if user is the host
  const room = await ctx.db.get(roomId);
  const isRoomHost = room && room.hostId === identity.subject;

  const canModerate = isRoomHost || (moderatorParticipant && (
    moderatorParticipant.role === "host" || 
    moderatorParticipant.role === "co-host" || 
    moderatorParticipant.role === "moderator"
  ));

  if (!canModerate) {
    throw new Error("You don't have moderation permissions");
  }

  return { identity, room, isRoomHost };
}

// Send a message
export const sendMessage = mutation({
  args: {
    roomId: v.id("rooms"),
    message: v.string(),
    messageType: v.optional(v.union(
      v.literal("text"),
      v.literal("file"),
      v.literal("image"),
      v.literal("system"),
      v.literal("announcement")
    )),
    fileData: v.optional(v.object({
      fileName: v.string(),
      fileSize: v.number(),
      fileType: v.string(),
      fileUrl: v.string(),
      thumbnailUrl: v.optional(v.string()),
    })),
    replyToMessageId: v.optional(v.id("chatMessages")),
    threadId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if user can send messages
    const userStatus = await ctx.db
      .query("chatUserStatus")
      .withIndex("by_room_user", (q: any) => 
        q.eq("roomId", args.roomId).eq("userId", identity.subject))
      .first();

    if (userStatus?.isMuted && 
        (!userStatus.mutedUntil || userStatus.mutedUntil > Date.now())) {
      throw new Error("You are muted and cannot send messages");
    }

    if (userStatus?.isBanned && 
        (!userStatus.bannedUntil || userStatus.bannedUntil > Date.now())) {
      throw new Error("You are banned from this chat");
    }

    const messageId = await ctx.db.insert("chatMessages", {
      roomId: args.roomId,
      userId: identity.subject,
      userName: identity.name || identity.email || "Unknown User",
      message: args.message,
      messageType: args.messageType || "text",
      fileData: args.fileData,
      replyToMessageId: args.replyToMessageId,
      threadId: args.threadId,
      isEdited: false,
      isDeleted: false,
      isFlagged: false,
      isModerated: false,
      timestamp: Date.now(),
    });

    return messageId;
  },
});

// Get messages for a room
export const getMessages = query({
  args: {
    roomId: v.id("rooms"),
    limit: v.optional(v.number()),
    before: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("chatMessages")
      .withIndex("by_room", (q: any) => q.eq("roomId", args.roomId))
      .filter((q: any) => q.eq(q.field("isDeleted"), false))
      .order("desc");

    if (args.before !== undefined) {
      query = query.filter((q: any) => q.lt(q.field("timestamp"), args.before!));
    }

    const messages = await query.take(args.limit || 50);
    return messages.reverse();
  },
});

// Add reaction to message
export const addReaction = mutation({
  args: {
    messageId: v.id("chatMessages"),
    reaction: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    // Check if user already reacted with this emoji
    const existingReaction = await ctx.db
      .query("chatReactions")
      .withIndex("by_user_message", (q: any) => 
        q.eq("userId", identity.subject).eq("messageId", args.messageId))
      .filter((q: any) => q.eq(q.field("reaction"), args.reaction))
      .first();

    if (existingReaction) {
      await ctx.db.delete(existingReaction._id);
      return { action: "removed" };
    } else {
      await ctx.db.insert("chatReactions", {
        messageId: args.messageId,
        userId: identity.subject,
        userName: identity.name || identity.email || "Unknown User",
        reaction: args.reaction,
        timestamp: Date.now(),
      });
      return { action: "added" };
    }
  },
});

// Edit message
export const editMessage = mutation({
  args: {
    messageId: v.id("chatMessages"),
    newMessage: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    if (message.userId !== identity.subject) {
      throw new Error("You can only edit your own messages");
    }

    await ctx.db.patch(args.messageId, {
      message: args.newMessage,
      isEdited: true,
      editedAt: Date.now(),
    });

    return { success: true };
  },
});

// Get user's chat status
export const getUserChatStatus = query({
  args: {
    roomId: v.id("rooms"),
    userId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }

    const userId = args.userId || identity.subject;
    
    const userStatus = await ctx.db
      .query("chatUserStatus")
      .withIndex("by_room_user", (q: any) => 
        q.eq("roomId", args.roomId).eq("userId", userId))
      .first();

    if (!userStatus) {
      return {
        canSendMessages: true,
        canSendFiles: true,
        canReact: true,
        canCreateThreads: true,
        isMuted: false,
        isBanned: false,
        messageCount: 0,
      };
    }

    return userStatus;
  },
});

// Moderate user
export const moderateUser = mutation({
  args: {
    roomId: v.id("rooms"),
    userId: v.string(),
    action: v.union(
      v.literal("muteUser"),
      v.literal("unmuteUser"),
      v.literal("timeoutUser"),
      v.literal("banUser"),
      v.literal("warnUser")
    ),
    duration: v.optional(v.number()),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { identity } = await checkModerationPermissions(ctx, args.roomId);

    // Get or create user status
    let userStatus = await ctx.db
      .query("chatUserStatus")
      .withIndex("by_room_user", (q: any) => 
        q.eq("roomId", args.roomId).eq("userId", args.userId))
      .first();

    if (!userStatus) {
      const userStatusId = await ctx.db.insert("chatUserStatus", {
        roomId: args.roomId,
        userId: args.userId,
        canSendMessages: true,
        canSendFiles: true,
        canReact: true,
        canCreateThreads: true,
        isMuted: false,
        isBanned: false,
        notifications: {
          mentions: true,
          reactions: true,
          replies: true,
          files: true,
        },
        messageCount: 0,
        timestamp: Date.now(),
        lastUpdated: Date.now(),
      });
      userStatus = await ctx.db.get(userStatusId);
    }

    // Apply moderation action
    const updates: any = { lastUpdated: Date.now() };
    
    switch (args.action) {
      case "muteUser":
        updates.isMuted = true;
        updates.mutedBy = identity.subject;
        if (args.duration) {
          updates.mutedUntil = Date.now() + args.duration;
        }
        break;
      case "unmuteUser":
        updates.isMuted = false;
        updates.mutedUntil = undefined;
        updates.mutedBy = undefined;
        break;
      case "timeoutUser":
        updates.isMuted = true;
        updates.mutedBy = identity.subject;
        updates.mutedUntil = Date.now() + (args.duration || 5 * 60 * 1000);
        break;
      case "banUser":
        updates.isBanned = true;
        updates.bannedBy = identity.subject;
        if (args.duration) {
          updates.bannedUntil = Date.now() + args.duration;
        }
        break;
    }

    if (userStatus?._id) {
      await ctx.db.patch(userStatus._id, updates);
    }

    return { success: true };
  },
});

// Get moderation actions for a room
export const getModerationActions = query({
  args: {
    roomId: v.id("rooms"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await checkModerationPermissions(ctx, args.roomId);

    return await ctx.db
      .query("chatModerationActions")
      .withIndex("by_room", (q: any) => q.eq("roomId", args.roomId))
      .order("desc")
      .take(args.limit || 50);
  },
});

// Get chat users for moderation
export const getChatUsers = query({
  args: {
    roomId: v.id("rooms"),
  },
  handler: async (ctx, args) => {
    await checkModerationPermissions(ctx, args.roomId);

    return await ctx.db
      .query("chatUserStatus")
      .withIndex("by_room", (q: any) => q.eq("roomId", args.roomId))
      .order("desc")
      .collect();
  },
});

// Get flagged messages
export const getFlaggedMessages = query({
  args: {
    roomId: v.id("rooms"),
  },
  handler: async (ctx, args) => {
    await checkModerationPermissions(ctx, args.roomId);

    return await ctx.db
      .query("chatMessages")
      .withIndex("by_room", (q: any) => q.eq("roomId", args.roomId))
      .filter((q: any) => q.eq(q.field("isFlagged"), true))
      .filter((q: any) => q.eq(q.field("isDeleted"), false))
      .order("desc")
      .take(50);
  },
});

// Delete message
export const deleteMessage = mutation({
  args: {
    messageId: v.id("chatMessages"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    // Check if user is the author or has moderation permissions
    const userIsAuthor = message.userId === identity.subject;
    const room = await ctx.db.get(message.roomId);
    const isHost = room && room.hostId === identity.subject;

    if (!userIsAuthor && !isHost) {
      throw new Error("You don't have permission to delete this message");
    }

    await ctx.db.patch(args.messageId, {
      isDeleted: true,
      deletedAt: Date.now(),
      deletedBy: identity.subject,
    });

    return { success: true };
  },
});

// Flag message
export const flagMessage = mutation({
  args: {
    messageId: v.id("chatMessages"),
    reason: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    await ctx.db.patch(args.messageId, {
      isFlagged: true,
      flaggedBy: identity.subject,
      flaggedReason: args.reason,
    });

    return { success: true };
  },
});

// Unflag message
export const unflagMessage = mutation({
  args: {
    messageId: v.id("chatMessages"),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    await checkModerationPermissions(ctx, message.roomId);

    await ctx.db.patch(args.messageId, {
      isFlagged: false,
      flaggedBy: undefined,
      flaggedReason: undefined,
    });

    return { success: true };
  },
});